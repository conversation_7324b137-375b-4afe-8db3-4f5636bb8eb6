/**
 * Commitlint 配置文件
 *
 * 该配置扩展了标准的 Conventional Commits 规范
 * 并使用 changelog-release 模块中定义的提交类型
 */

import { COMMIT_TYPES } from './scripts/changelog-release/constants.js';

// 从 COMMIT_TYPES 中提取类型列表
const typeEnum = Object.keys(COMMIT_TYPES);

export default {
  // 扩展标准的 Conventional Commits 配置
  extends: ['@commitlint/config-conventional'],

  // 自定义规则
  rules: {
    // 标准规则配置
    'type-enum': [
      2,
      'always',
      typeEnum, // 使用从 COMMIT_TYPES 导入的类型
    ],

    // 主题行长度限制
    'subject-max-length': [2, 'always', 100],
    'subject-min-length': [2, 'always', 5],

    // 正文长度限制
    'body-max-line-length': [2, 'always', 200],
  },
};
