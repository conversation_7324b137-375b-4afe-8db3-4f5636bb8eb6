/**
 * @fileoverview Commitizen 适配器
 * @description 为 Commitizen 提供自定义的提交消息生成器，支持 Applies-To 和 Issue-ID 字段
 */

import { COMMIT_TYPES, REGEX_PATTERNS } from './scripts/changelog-release/constants.js';
import { listExtensions } from './scripts/extension-config/utils.js';

/**
 * Commitizen 提示器接口
 */
interface CommitizenPrompter {
  prompt: (questions: any[]) => Promise<any>;
}

/**
 * 提交函数类型
 */
type CommitFunction = (message: string) => void;

/**
 * 提交答案接口
 */
interface CommitAnswers {
  type: string;
  scope?: string;
  subject: string;
  body?: string;
  appliesTo?: string[];
  issueId?: string;
  isBreaking: boolean;
  breakingChange?: string;
}

/**
 * 生成 Commitizen 的提交类型选项
 */
function generateCommitTypeChoices() {
  return Object.entries(COMMIT_TYPES).map(([type, config]) => {
    return {
      value: type,
      name: `${type}:${' '.repeat(Math.max(1, 10 - type.length))}${config.description}`,
      short: config.description,
    };
  });
}

/**
 * 获取可用的扩展插件列表
 */
async function getAvailableExtensions() {
  try {
    return listExtensions();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.warn('无法获取扩展列表，使用默认选项:', errorMessage);
    return ['cookies_manager', 'price_tracker', 'extension_dashboard'];
  }
}

/**
 * Commitizen 适配器配置
 */
export default {
  prompter(cz: CommitizenPrompter, commit: CommitFunction) {
    console.log('\n🎯 欢迎使用 Changelog & Release 提交向导!\n');
    console.log('📋 请按照提示填写提交信息，确保符合项目规范。\n');

    cz.prompt([
      {
        type: 'list',
        name: 'type',
        message: '选择提交类型:',
        choices: generateCommitTypeChoices(),
        pageSize: 12,
      },
      {
        type: 'input',
        name: 'scope',
        message: '影响范围 (可选，如: auth, ui, api):',
        filter: (value: string) => value.trim(),
      },
      {
        type: 'input',
        name: 'subject',
        message: '简短描述 (必填):',
        validate: (value: string) => {
          if (value.trim().length === 0) {
            return '提交描述不能为空';
          }
          if (value.trim().length > 72) {
            return '提交描述不能超过 72 个字符';
          }
          return true;
        },
        filter: (value: string) => value.trim(),
      },
      {
        type: 'input',
        name: 'body',
        message: '详细描述 (可选，支持多行):',
        filter: (value: string) => value.trim(),
      },
      {
        type: 'checkbox',
        name: 'appliesTo',
        message: '✅ 应用到哪些扩展 (可选):',
        choices: async () => {
          const extensions = await getAvailableExtensions();
          return extensions.map(ext => ({
            name: ext,
            value: ext,
            checked: false,
          }));
        },
        pageSize: 10,
      },
      {
        type: 'input',
        name: 'issueId',
        message: '🔗 关联的 Issue ID (可选，格式: 1#20250712-01):',
        validate: (value: string) => {
          if (value.trim() === '') {
            return true; // 可选字段
          }
          // 支持两种格式: 1#20250712-01 或 20250712-01
          if (!REGEX_PATTERNS.issueId.test(value.trim())) {
            return 'Issue ID 格式不正确，应为: 数字#日期-序号 (如: 1#20250712-01) 或 日期-序号 (如: #20250712-01)';
          }
          return true;
        },
        filter: (value: string) => value.trim(),
      },
      {
        type: 'confirm',
        name: 'isBreaking',
        message: '💥 是否为重大变更 (Breaking Change)?',
        default: false,
      },
      {
        type: 'input',
        name: 'breakingChange',
        message: '📝 描述重大变更的影响 (可选):',
        when: (answers: CommitAnswers) => answers.isBreaking,
        filter: (value: string) => value.trim(),
      },
    ]).then((answers: CommitAnswers) => {
      const {
        type,
        scope,
        subject,
        body,
        appliesTo,
        issueId,
        isBreaking,
        breakingChange,
      } = answers;

      // 构建提交消息头部
      const scopeStr = scope ? `(${scope})` : '';
      const breakingStr = isBreaking ? '!' : '';
      const header = `${type}${scopeStr}${breakingStr}: ${subject}`;

      // 构建提交消息正文
      const bodyParts = [];
      
      if (body) {
        bodyParts.push(body);
        bodyParts.push(''); // 空行分隔
      }

      if (isBreaking && breakingChange) {
        bodyParts.push(`**重大变更**: ${breakingChange}`);
        bodyParts.push(''); // 空行分隔
      }

      // 添加元数据
      if (appliesTo && appliesTo.length > 0) {
        bodyParts.push(`Applies-To: ${appliesTo.join(', ')}`);
      }

      if (issueId) {
        bodyParts.push(`Issue-ID: ${issueId}`);
      }

      // 组装完整的提交消息
      const fullMessage = bodyParts.length > 0 
        ? `${header}\n\n${bodyParts.join('\n')}`
        : header;

      // 显示预览
      console.log('\n📋 提交消息预览:');
      console.log('─'.repeat(50));
      console.log(fullMessage);
      console.log('─'.repeat(50));

      commit(fullMessage);
    });
  },
};

